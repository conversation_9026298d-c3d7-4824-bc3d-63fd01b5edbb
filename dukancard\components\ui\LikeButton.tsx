'use client';

import React, { useEffect, useState } from 'react';
import { Heart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatIndianNumberShort } from '@/lib/utils/formatNumber';
import { useLikeCommentStore } from '@/lib/stores/likeCommentStore';

interface LikeButtonProps {
  postId: string;
  postSource: 'business' | 'customer';
  initialLikeCount?: number;
  initialIsLiked?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal';
  showCount?: boolean;
  className?: string;
  disabled?: boolean;
  onLikeChange?: (isLiked: boolean, likeCount: number) => void;
}

export function LikeButton({
  postId,
  postSource,
  initialLikeCount = 0,
  initialIsLiked = false,
  size = 'md',
  variant = 'default',
  showCount = true,
  className,
  disabled = false,
  onLikeChange,
}: LikeButtonProps) {
  const {
    postLikes,
    likingPosts,
    togglePostLike,
    getPostLikeStatus,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const postLikeStatus = postLikes[postKey];
  const isLoading = likingPosts.has(postKey);

  // Use store state if available, otherwise use initial values
  const isLiked = postLikeStatus?.is_liked ?? initialIsLiked;
  const likeCount = postLikeStatus?.like_count ?? initialLikeCount;

  // Load initial like status if not in store
  useEffect(() => {
    if (!postLikeStatus) {
      getPostLikeStatus(postId, postSource);
    }
  }, [postId, postSource, postLikeStatus, getPostLikeStatus]);

  // Notify parent of changes
  useEffect(() => {
    if (onLikeChange && postLikeStatus) {
      onLikeChange(postLikeStatus.is_liked, postLikeStatus.like_count);
    }
  }, [postLikeStatus, onLikeChange]);

  const handleClick = async () => {
    if (disabled || isLoading) return;

    try {
      await togglePostLike(postId, postSource);
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      icon: 'h-4 w-4',
      button: 'px-2 py-1 text-xs',
      gap: 'gap-1',
    },
    md: {
      icon: 'h-5 w-5',
      button: 'px-3 py-2 text-sm',
      gap: 'gap-2',
    },
    lg: {
      icon: 'h-6 w-6',
      button: 'px-4 py-2 text-base',
      gap: 'gap-2',
    },
  };

  const config = sizeConfig[size];

  // Variant styles
  const variantStyles = {
    default: cn(
      'inline-flex items-center rounded-full border transition-all duration-200',
      'hover:scale-105 active:scale-95',
      'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
      isLiked
        ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
        : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300',
      disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
      config.button,
      config.gap
    ),
    minimal: cn(
      'inline-flex items-center transition-all duration-200',
      'hover:scale-110 active:scale-95',
      'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded',
      isLiked ? 'text-red-600' : 'text-gray-600 hover:text-red-500',
      disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
      config.gap
    ),
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={cn(variantStyles[variant], className)}
      aria-label={isLiked ? 'Unlike post' : 'Like post'}
      aria-pressed={isLiked}
    >
      {/* Heart Icon */}
      <div className="relative">
        <Heart
          className={cn(
            config.icon,
            'transition-all duration-200',
            isLiked ? 'fill-current' : 'fill-none',
            isLoading && 'animate-pulse'
          )}
        />
        
        {/* Loading indicator */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={cn(
              'border-2 border-current border-t-transparent rounded-full animate-spin',
              size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'
            )} />
          </div>
        )}
      </div>

      {/* Like Count */}
      {showCount && (
        <span className={cn(
          'font-medium transition-all duration-200',
          isLoading && 'opacity-50'
        )}>
          {formatIndianNumberShort(likeCount)}
        </span>
      )}

      {/* Screen reader text */}
      <span className="sr-only">
        {isLiked ? 'Unlike this post' : 'Like this post'}
        {showCount && `, ${likeCount} ${likeCount === 1 ? 'like' : 'likes'}`}
      </span>
    </button>
  );
}

// Hook for using like button functionality without UI
export function useLikeButton(postId: string, postSource: 'business' | 'customer') {
  const {
    postLikes,
    likingPosts,
    togglePostLike,
    getPostLikeStatus,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const postLikeStatus = postLikes[postKey];
  const isLoading = likingPosts.has(postKey);

  const isLiked = postLikeStatus?.is_liked ?? false;
  const likeCount = postLikeStatus?.like_count ?? 0;

  // Load initial like status if not in store
  useEffect(() => {
    if (!postLikeStatus) {
      getPostLikeStatus(postId, postSource);
    }
  }, [postId, postSource, postLikeStatus, getPostLikeStatus]);

  const handleToggle = async () => {
    if (isLoading) return;
    
    try {
      await togglePostLike(postId, postSource);
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  };

  return {
    isLiked,
    likeCount,
    isLoading,
    toggleLike: handleToggle,
  };
}

export default LikeButton;
