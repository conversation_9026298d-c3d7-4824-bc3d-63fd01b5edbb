import { supabase } from '@/lib/supabase';
import { TABLES, COLUMNS, RPC_FUNCTIONS } from '@/src/config/supabase/constants';
import type {
  LikeCommentRequest,
  UnlikeCommentRequest,
  LikeCommentResponse,
  UnlikeCommentResponse,
  CommentLikeStatus,
} from '@/lib/types/like-comment';

/**
 * Like a comment
 */
export async function likeComment(commentId: string): Promise<LikeCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.LIKE_COMMENT, {
      p_comment_id: commentId,
    } as LikeCommentRequest);

    if (error) {
      console.error('Error liking comment:', error);
      return {
        success: false,
        message: 'Failed to like comment',
        error: error.message,
      };
    }

    return data as LikeCommentResponse;
  } catch (error) {
    console.error('Error in likeComment:', error);
    return {
      success: false,
      message: 'Failed to like comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Unlike a comment
 */
export async function unlikeComment(commentId: string): Promise<UnlikeCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.UNLIKE_COMMENT, {
      p_comment_id: commentId,
    } as UnlikeCommentRequest);

    if (error) {
      console.error('Error unliking comment:', error);
      return {
        success: false,
        message: 'Failed to unlike comment',
        error: error.message,
      };
    }

    return data as UnlikeCommentResponse;
  } catch (error) {
    console.error('Error in unlikeComment:', error);
    return {
      success: false,
      message: 'Failed to unlike comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get like status for a comment
 */
export async function getCommentLikeStatus(commentId: string): Promise<CommentLikeStatus> {
  try {
    // Get total like count
    const { count: likeCount } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select('*', { count: 'exact', head: true })
      .eq(COLUMNS.COMMENT_ID, commentId);

    // Check if current user has liked the comment
    const { data: { user } } = await supabase.auth.getUser();
    let isLiked = false;

    if (user) {
      const { data: userLike } = await supabase
        .from(TABLES.COMMENT_LIKES)
        .select(COLUMNS.ID)
        .eq(COLUMNS.USER_ID, user.id)
        .eq(COLUMNS.COMMENT_ID, commentId)
        .maybeSingle();

      isLiked = !!userLike;
    }

    return {
      is_liked: isLiked,
      like_count: likeCount || 0,
    };
  } catch (error) {
    console.error('Error getting comment like status:', error);
    return {
      is_liked: false,
      like_count: 0,
    };
  }
}

/**
 * Get users who liked a comment with their profile information
 */
export async function getCommentLikers(
  commentId: string,
  limit: number = 20,
  offset: number = 0
) {
  try {
    // First get the likes
    const { data: likes, error } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.USER_ID}
      `)
      .eq(COLUMNS.COMMENT_ID, commentId)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching comment likers:', error);
      return {
        success: false,
        data: [],
        error: error.message,
      };
    }

    if (!likes || likes.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Get user IDs for fetching profile information
    const userIds = likes.map((like: any) => like.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url, business_slug')
      .in('id', userIds);

    // Fetch customer profiles
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);

    // Enhance likes with user profile information
    const likersWithProfile = likes.map((like: any) => {
      const businessProfile = businessProfileMap.get(like.user_id);
      const customerProfile = customerProfileMap.get(like.user_id);

      let user_name = 'Unknown User';
      let user_avatar = undefined;
      let user_type: 'business' | 'customer' = 'customer';
      let user_slug = undefined;

      if (businessProfile) {
        user_name = businessProfile.business_name || 'Business';
        user_avatar = businessProfile.logo_url;
        user_type = 'business';
        user_slug = businessProfile.business_slug;
      } else if (customerProfile) {
        user_name = customerProfile.name || 'Customer';
        user_avatar = customerProfile.avatar_url;
        user_type = 'customer';
      }

      return {
        id: like.id,
        user_id: like.user_id,
        user_name,
        user_avatar,
        user_type,
        user_slug,
        created_at: like.created_at,
      };
    });

    return {
      success: true,
      data: likersWithProfile,
    };
  } catch (error) {
    console.error('Error in getCommentLikers:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Toggle like status for a comment (like if not liked, unlike if liked)
 */
export async function toggleCommentLike(
  commentId: string
): Promise<LikeCommentResponse | UnlikeCommentResponse> {
  try {
    const currentStatus = await getCommentLikeStatus(commentId);
    
    if (currentStatus.is_liked) {
      return await unlikeComment(commentId);
    } else {
      return await likeComment(commentId);
    }
  } catch (error) {
    console.error('Error toggling comment like:', error);
    return {
      success: false,
      message: 'Failed to toggle like',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
