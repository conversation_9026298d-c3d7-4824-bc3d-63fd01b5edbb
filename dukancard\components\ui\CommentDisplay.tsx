'use client';

import React, { useState } from 'react';
import { Heart, MessageCircle, Pin, MoreHorizontal, Edit, Trash2, Flag } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatIndianNumberShort } from '@/lib/utils/formatNumber';
import type { PostCommentWithUser } from '@/types/like-comment';
import CommentInput from './CommentInput';
import { useLikeCommentStore } from '@/lib/stores/likeCommentStore';

interface CommentDisplayProps {
  comment: PostCommentWithUser;
  postId: string;
  postSource: 'business' | 'customer';
  currentUserId?: string;
  isPostOwner?: boolean;
  level?: number;
  className?: string;
  onReply?: (commentId: string) => void;
  onEdit?: (commentId: string, content: string) => void;
  onDelete?: (commentId: string) => void;
  onPin?: (commentId: string) => void;
  onUnpin?: (commentId: string) => void;
}

export function CommentDisplay({
  comment,
  postId,
  postSource,
  currentUserId,
  isPostOwner = false,
  level = 0,
  className,
  onReply,
  onEdit,
  onDelete,
  onPin,
  onUnpin,
}: CommentDisplayProps) {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [showEditInput, setShowEditInput] = useState(false);
  const [showActions, setShowActions] = useState(false);
  
  const {
    commentLikes,
    likingComments,
    toggleCommentLike,
    getCommentLikeStatus,
    editComment,
    deleteComment,
    pinComment,
    unpinComment,
  } = useLikeCommentStore();

  const commentLikeStatus = commentLikes[comment.id];
  const isLikingComment = likingComments.has(comment.id);
  
  // Use store state if available, otherwise use comment data
  const isLiked = commentLikeStatus?.is_liked ?? comment.is_liked_by_current_user;
  const likeCount = commentLikeStatus?.like_count ?? comment.like_count;

  const isOwner = currentUserId === comment.user_id;
  const canEdit = isOwner;
  const canDelete = isOwner || isPostOwner;
  const canPin = isPostOwner && !comment.parent_comment_id; // Only pin top-level comments
  const canReply = level === 0; // Only allow replies to top-level comments

  // Load comment like status if not in store
  React.useEffect(() => {
    if (!commentLikeStatus) {
      getCommentLikeStatus(comment.id);
    }
  }, [comment.id, commentLikeStatus, getCommentLikeStatus]);

  const handleLike = async () => {
    if (isLikingComment) return;
    
    try {
      await toggleCommentLike(comment.id);
    } catch (error) {
      console.error('Error toggling comment like:', error);
    }
  };

  const handleReply = () => {
    setShowReplyInput(true);
    onReply?.(comment.id);
  };

  const handleEdit = () => {
    setShowEditInput(true);
  };

  const handleEditSubmit = async (content: string) => {
    try {
      const success = await editComment(comment.id, content);
      if (success) {
        setShowEditInput(false);
        onEdit?.(comment.id, content);
      }
    } catch (error) {
      console.error('Error editing comment:', error);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      try {
        const success = await deleteComment(comment.id, postId, postSource);
        if (success) {
          onDelete?.(comment.id);
        }
      } catch (error) {
        console.error('Error deleting comment:', error);
      }
    }
  };

  const handlePin = async () => {
    try {
      if (comment.is_pinned) {
        const success = await unpinComment(comment.id, postId, postSource);
        if (success) {
          onUnpin?.(comment.id);
        }
      } else {
        const success = await pinComment(comment.id, postId, postSource);
        if (success) {
          onPin?.(comment.id);
        }
      }
    } catch (error) {
      console.error('Error toggling pin status:', error);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className={cn(
      'group',
      level > 0 && 'ml-8 border-l-2 border-gray-100 pl-4',
      className
    )}>
      <div className="flex gap-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
            {comment.user_name.charAt(0).toUpperCase()}
          </div>
        </div>

        {/* Comment content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900 text-sm">
              {comment.user_name}
            </span>
            
            {/* User badge */}
            <span className={cn(
              'px-2 py-0.5 rounded-full text-xs font-medium',
              comment.user_type === 'business'
                ? 'bg-blue-100 text-blue-700'
                : 'bg-green-100 text-green-700'
            )}>
              {comment.user_type === 'business' ? 'Business' : 'Customer'}
            </span>

            {/* Pinned badge */}
            {comment.is_pinned && (
              <div className="flex items-center gap-1 px-2 py-0.5 bg-amber-100 text-amber-700 rounded-full text-xs font-medium">
                <Pin className="h-3 w-3" />
                Pinned
              </div>
            )}

            {/* Timestamp */}
            <span className="text-gray-500 text-xs">
              {formatTimeAgo(comment.created_at)}
            </span>

            {/* Edited indicator */}
            {comment.is_edited && (
              <span className="text-gray-400 text-xs">(edited)</span>
            )}

            {/* Actions menu */}
            <div className="relative ml-auto">
              <button
                onClick={() => setShowActions(!showActions)}
                className="opacity-0 group-hover:opacity-100 p-1 rounded-full hover:bg-gray-100 transition-all duration-200"
                aria-label="Comment actions"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-500" />
              </button>

              {showActions && (
                <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
                  {canEdit && (
                    <button
                      onClick={handleEdit}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Edit
                    </button>
                  )}
                  
                  {canPin && (
                    <button
                      onClick={handlePin}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Pin className="h-4 w-4" />
                      {comment.is_pinned ? 'Unpin' : 'Pin'}
                    </button>
                  )}
                  
                  {canDelete && (
                    <button
                      onClick={handleDelete}
                      className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </button>
                  )}
                  
                  {!isOwner && (
                    <button
                      onClick={() => {/* TODO: Implement report */}}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Flag className="h-4 w-4" />
                      Report
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Comment text or edit input */}
          {showEditInput ? (
            <div className="mb-3">
              <CommentInput
                postId={postId}
                postSource={postSource}
                placeholder="Edit your comment..."
                autoFocus
                showCancel
                onSubmit={handleEditSubmit}
                onCancel={() => setShowEditInput(false)}
                className="mb-0"
              />
            </div>
          ) : (
            <p className="text-gray-800 text-sm mb-3 whitespace-pre-wrap break-words">
              {comment.content}
            </p>
          )}

          {/* Actions */}
          <div className="flex items-center gap-4 text-sm">
            {/* Like button */}
            <button
              onClick={handleLike}
              disabled={isLikingComment}
              className={cn(
                'flex items-center gap-1 transition-colors duration-200',
                'hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded',
                isLiked ? 'text-red-600' : 'text-gray-500',
                isLikingComment && 'opacity-50 cursor-not-allowed'
              )}
              aria-label={isLiked ? 'Unlike comment' : 'Like comment'}
            >
              <Heart className={cn(
                'h-4 w-4 transition-all duration-200',
                isLiked ? 'fill-current' : 'fill-none',
                isLikingComment && 'animate-pulse'
              )} />
              {likeCount > 0 && (
                <span className="font-medium">
                  {formatIndianNumberShort(likeCount)}
                </span>
              )}
            </button>

            {/* Reply button */}
            {canReply && (
              <button
                onClick={handleReply}
                className="flex items-center gap-1 text-gray-500 hover:text-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
              >
                <MessageCircle className="h-4 w-4" />
                Reply
              </button>
            )}
          </div>

          {/* Reply input */}
          {showReplyInput && (
            <div className="mt-3">
              <CommentInput
                postId={postId}
                postSource={postSource}
                parentCommentId={comment.id}
                placeholder={`Reply to ${comment.user_name}...`}
                autoFocus
                showCancel
                onSubmit={() => setShowReplyInput(false)}
                onCancel={() => setShowReplyInput(false)}
              />
            </div>
          )}

          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 space-y-4">
              {comment.replies.map((reply) => (
                <CommentDisplay
                  key={reply.id}
                  comment={reply}
                  postId={postId}
                  postSource={postSource}
                  currentUserId={currentUserId}
                  isPostOwner={isPostOwner}
                  level={level + 1}
                  onEdit={onEdit}
                  onDelete={onDelete}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close actions menu */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(false)}
        />
      )}
    </div>
  );
}

export default CommentDisplay;
