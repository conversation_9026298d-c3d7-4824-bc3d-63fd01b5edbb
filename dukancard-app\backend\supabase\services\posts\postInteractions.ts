/**
 * Post Interactions Service for React Native
 * Handles like, comment, and share functionality for posts
 */

import { Share } from 'react-native';
import {
  likePost as likePostRPC,
  unlikePost as unlikePostRPC,
  getPostLikeStatus as getPostLikeStatusRPC
} from '../likes/postLikes';

export interface PostInteractionResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface PostLikeStatus {
  isLiked: boolean;
  likeCount: number;
}

/**
 * Like a post (business or customer post)
 */
export async function likePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostInteractionResponse> {
  const result = await likePostRPC(postId, postSource);
  return {
    success: result.success,
    message: result.message,
    error: result.error,
  };
}

/**
 * Unlike a post (business or customer post)
 */
export async function unlikePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostInteractionResponse> {
  const result = await unlikePostRPC(postId, postSource);
  return {
    success: result.success,
    message: result.message,
    error: result.error,
  };
}

/**
 * Get like status for a post
 */
export async function getPostLikeStatus(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostLikeStatus> {
  const result = await getPostLikeStatusRPC(postId, postSource);
  return {
    isLiked: result.is_liked,
    likeCount: result.like_count,
  };
}

/**
 * Share a post using React Native Share API
 */
export async function sharePost(
  postId: string,
  postContent: string,
  authorName: string,
  _businessSlug?: string
): Promise<PostInteractionResponse> {
  try {
    // Always share the post URL, not the business slug
    const shareUrl = `https://dukancard.in/post/${postId}`;

    const shareMessage = `Check out this post by ${authorName}:\n\n"${postContent}"\n\n${shareUrl}`;

    const result = await Share.share({
      message: shareMessage,
      url: shareUrl,
      title: `Post by ${authorName}`,
    });

    if (result.action === Share.sharedAction) {
      return { 
        success: true, 
        message: 'Post shared successfully' 
      };
    } else if (result.action === Share.dismissedAction) {
      return { 
        success: false, 
        message: 'Share cancelled' 
      };
    }

    return { 
      success: true, 
      message: 'Share completed' 
    };
  } catch (error) {
    console.error('Error sharing post:', error);
    return { 
      success: false, 
      message: 'Failed to share post',
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Utility function to handle comment interactions
 * This can be used by components that need to trigger comment functionality
 */
export function showComments(
  postId: string,
  postSource: 'business' | 'customer',
  onShowComments?: (postId: string, postSource: 'business' | 'customer') => void
): void {
  if (onShowComments) {
    onShowComments(postId, postSource);
  } else {
    // Default behavior - could be used for navigation or other actions
    console.log(`Opening comments for ${postSource} post: ${postId}`);
  }
}
