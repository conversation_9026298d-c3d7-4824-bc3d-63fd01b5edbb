import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { MessageCircle, RefreshCw } from 'lucide-react-native';import CommentDisplay from './CommentDisplay';
import CommentInput from './CommentInput';
import type { PostCommentWithUser } from '@/lib/types/like-comment';
import { useLikeCommentStore } from '@/src/stores/likeCommentStore';

interface CommentSectionProps {
  postId: string;
  postSource: 'business' | 'customer';
  currentUserId?: string;
  isPostOwner?: boolean;
  showInput?: boolean;
  onCommentCountChange?: (count: number) => void;
}

export function CommentSection({
  postId,
  postSource,
  currentUserId,
  isPostOwner = false,
  showInput = true,
  onCommentCountChange,
}: CommentSectionProps) {
  const [sortOrder, setSortOrder] = useState<'pinned_first' | 'newest' | 'oldest'>('pinned_first');
  const [refreshing, setRefreshing] = useState(false);
  
  const {
    postComments,
    loadingComments,
    loadComments,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const commentsData = postComments[postKey];
  const isLoading = loadingComments.has(postKey);
  
  const comments = commentsData?.comments || [];
  const hasComments = comments.length > 0;

  // Load comments on mount
  useEffect(() => {
    loadComments(postId, postSource);
  }, [postId, postSource, loadComments]);

  // Notify parent of comment count changes
  useEffect(() => {
    onCommentCountChange?.(comments.length);
  }, [comments.length, onCommentCountChange]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadComments(postId, postSource, true);
    setRefreshing(false);
  };

  // Sort comments based on selected order
  const sortedComments = React.useMemo(() => {
    const sorted = [...comments];
    
    switch (sortOrder) {
      case 'pinned_first':
        return sorted.sort((a, b) => {
          if (a.is_pinned && !b.is_pinned) return -1;
          if (!a.is_pinned && b.is_pinned) return 1;
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });
      case 'newest':
        return sorted.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case 'oldest':
        return sorted.sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      default:
        return sorted;
    }
  }, [comments, sortOrder]);

  const renderComment = ({ item }: { item: PostCommentWithUser }) => (
    <CommentDisplay
      comment={item}
      postId={postId}
      postSource={postSource}
      currentUserId={currentUserId}
      isPostOwner={isPostOwner}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <MessageCircle size={20} color="#6b7280" />
        <Text style={styles.headerTitle}>
          Comments {hasComments && `(${comments.length})`}
        </Text>
      </View>

      <View style={styles.headerRight}>
        {/* Sort picker */}
        {hasComments && (
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => {
              // Cycle through sort orders
              const orders: typeof sortOrder[] = ['pinned_first', 'newest', 'oldest'];
              const currentIndex = orders.indexOf(sortOrder);
              const nextIndex = (currentIndex + 1) % orders.length;
              setSortOrder(orders[nextIndex]);
            }}
          >
            <Text style={styles.sortButtonText}>
              {sortOrder === 'pinned_first' ? 'Pinned First' : 
               sortOrder === 'newest' ? 'Newest' : 'Oldest'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Refresh button */}
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw 
            size={16} 
            color="#6b7280"
            style={isLoading ? { transform: [{ rotate: '180deg' }] } : {}}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <MessageCircle size={48} color="#d1d5db" />
      <Text style={styles.emptyTitle}>No comments yet</Text>
      <Text style={styles.emptySubtitle}>
        Be the first to share your thoughts!
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (isLoading && hasComments) {
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator size="small" color="#6b7280" />
          <Text style={styles.loadingText}>Refreshing comments...</Text>
        </View>
      );
    }
    return null;
  };

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      {/* Comment input */}
      {showInput && (
        <View style={styles.inputContainer}>
          <CommentInput
            postId={postId}
            postSource={postSource}
            placeholder="Write a comment..."
          />
        </View>
      )}

      {/* Comments list */}
      {isLoading && !hasComments ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6b7280" />
          <Text style={styles.loadingText}>Loading comments...</Text>
        </View>
      ) : (
        <FlatList
          data={sortedComments}
          renderItem={renderComment}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={renderEmpty}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="#6b7280"
              colors={["#6b7280"]}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.listContent,
            !hasComments && styles.listContentEmpty,
          ]}
        />
      )}

      {/* Footer with stats */}
      {hasComments && (
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
          </Text>
          
          {commentsData?.lastFetched && (
            <Text style={styles.footerText}>
              Last updated: {new Date(commentsData.lastFetched).toLocaleTimeString()}
            </Text>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
  },
  sortButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
  refreshButton: {
    padding: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
  },
  inputContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  listContentEmpty: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  loadingFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  loadingText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  footerText: {
    fontSize: 12,
    color: '#6b7280',
  },
});

export default CommentSection;
