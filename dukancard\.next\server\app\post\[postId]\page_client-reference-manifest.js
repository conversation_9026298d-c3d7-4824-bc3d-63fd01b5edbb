globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/post/[postId]/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next-themes/dist/index.mjs <module evaluation>":{"id":"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next-themes/dist/index.mjs":{"id":"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/components/ui/sonner.tsx <module evaluation>":{"id":"[project]/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/components/ui/sonner.tsx":{"id":"[project]/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/components/GoogleAnalytics.tsx <module evaluation>":{"id":"[project]/app/components/GoogleAnalytics.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/components/GoogleAnalytics.tsx":{"id":"[project]/app/components/GoogleAnalytics.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/components/MetaPixel.tsx <module evaluation>":{"id":"[project]/app/components/MetaPixel.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/components/MetaPixel.tsx":{"id":"[project]/app/components/MetaPixel.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/post/[postId]/error.tsx <module evaluation>":{"id":"[project]/app/post/[postId]/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_f2cc4642._.js","/_next/static/chunks/app_post_%5BpostId%5D_error_tsx_0f887fde._.js"],"async":false},"[project]/app/post/[postId]/error.tsx":{"id":"[project]/app/post/[postId]/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_f2cc4642._.js","/_next/static/chunks/app_post_%5BpostId%5D_error_tsx_0f887fde._.js"],"async":false},"[project]/components/feed/shared/PostCardSkeleton.tsx <module evaluation>":{"id":"[project]/components/feed/shared/PostCardSkeleton.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_09cb0a30._.js","/_next/static/chunks/_79b94857._.js","/_next/static/chunks/app_post_%5BpostId%5D_loading_tsx_0f887fde._.js"],"async":false},"[project]/components/feed/shared/PostCardSkeleton.tsx":{"id":"[project]/components/feed/shared/PostCardSkeleton.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_09cb0a30._.js","/_next/static/chunks/_79b94857._.js","/_next/static/chunks/app_post_%5BpostId%5D_loading_tsx_0f887fde._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_next_dist_b758c999._.js","/_next/static/chunks/app_post_%5BpostId%5D_not-found_tsx_0f887fde._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_next_dist_b758c999._.js","/_next/static/chunks/app_post_%5BpostId%5D_not-found_tsx_0f887fde._.js"],"async":false},"[project]/components/post/SinglePostView.tsx <module evaluation>":{"id":"[project]/components/post/SinglePostView.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false},"[project]/components/post/SinglePostView.tsx":{"id":"[project]/components/post/SinglePostView.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false},"[project]/components/post/BackNavigation.tsx <module evaluation>":{"id":"[project]/components/post/BackNavigation.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false},"[project]/components/post/BackNavigation.tsx":{"id":"[project]/components/post/BackNavigation.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false},"[project]/components/post/ConditionalPostLayout.tsx <module evaluation>":{"id":"[project]/components/post/ConditionalPostLayout.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false},"[project]/components/post/ConditionalPostLayout.tsx":{"id":"[project]/components/post/ConditionalPostLayout.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/_faab6129._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_16fb523b._.js","/_next/static/chunks/_16c4dad9._.js","/_next/static/chunks/node_modules_next_b6a95703._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","/_next/static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","/_next/static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","/_next/static/chunks/node_modules_%40supabase_auth-js_dist_module_6004bf20._.js","/_next/static/chunks/node_modules_%40dnd-kit_core_dist_core_esm_deb93fb9.js","/_next/static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","/_next/static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","/_next/static/chunks/node_modules_%40radix-ui_7a78c17e._.js","/_next/static/chunks/node_modules_%40supabase_4230e2a7._.js","/_next/static/chunks/node_modules_%40floating-ui_9ec1fa39._.js","/_next/static/chunks/node_modules_ebcf3fc1._.js","/_next/static/chunks/app_post_%5BpostId%5D_page_tsx_0f887fde._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js"],"async":false}},"[project]/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/ui/sonner.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js"],"async":false}},"[project]/app/components/GoogleAnalytics.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/GoogleAnalytics.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js"],"async":false}},"[project]/app/components/MetaPixel.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/MetaPixel.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js"],"async":false}},"[project]/app/post/[postId]/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/post/[postId]/error.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f66052e6._.js","server/chunks/ssr/node_modules_6cb972b3._.js","server/chunks/ssr/[root-of-the-server]__a1441f65._.js"],"async":false}},"[project]/components/feed/shared/PostCardSkeleton.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/feed/shared/PostCardSkeleton.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/node_modules_7cfbfa79._.js","server/chunks/ssr/_a96045a6._.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f64c26ec._.js","server/chunks/ssr/node_modules_79828b1e._.js","server/chunks/ssr/[root-of-the-server]__e8bca49f._.js"],"async":false}},"[project]/components/post/SinglePostView.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/SinglePostView.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/[root-of-the-server]__ebc9aef7._.js","server/chunks/ssr/[root-of-the-server]__1c7c892a._.js","server/chunks/ssr/node_modules_next_0182360a._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_da255d2b._.js","server/chunks/ssr/node_modules_motion-dom_dist_es_47fe6a6b._.js","server/chunks/ssr/node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_59f71f6a._.js","server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_080f843b._.js","server/chunks/ssr/node_modules_@dnd-kit_core_dist_core_esm_25658ffe.js","server/chunks/ssr/node_modules_html5-qrcode_esm_07f61283._.js","server/chunks/ssr/node_modules_html5-qrcode_third_party_zxing-js_umd_e9f276bd.js","server/chunks/ssr/node_modules_@radix-ui_9ca9bb00._.js","server/chunks/ssr/node_modules_@supabase_1d116629._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_9ed906ca._.js"],"async":false}},"[project]/components/post/BackNavigation.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/BackNavigation.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/[root-of-the-server]__ebc9aef7._.js","server/chunks/ssr/[root-of-the-server]__1c7c892a._.js","server/chunks/ssr/node_modules_next_0182360a._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_da255d2b._.js","server/chunks/ssr/node_modules_motion-dom_dist_es_47fe6a6b._.js","server/chunks/ssr/node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_59f71f6a._.js","server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_080f843b._.js","server/chunks/ssr/node_modules_@dnd-kit_core_dist_core_esm_25658ffe.js","server/chunks/ssr/node_modules_html5-qrcode_esm_07f61283._.js","server/chunks/ssr/node_modules_html5-qrcode_third_party_zxing-js_umd_e9f276bd.js","server/chunks/ssr/node_modules_@radix-ui_9ca9bb00._.js","server/chunks/ssr/node_modules_@supabase_1d116629._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_9ed906ca._.js"],"async":false}},"[project]/components/post/ConditionalPostLayout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/ConditionalPostLayout.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__50d88af7._.js","server/chunks/ssr/[root-of-the-server]__ebc9aef7._.js","server/chunks/ssr/[root-of-the-server]__1c7c892a._.js","server/chunks/ssr/node_modules_next_0182360a._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_da255d2b._.js","server/chunks/ssr/node_modules_motion-dom_dist_es_47fe6a6b._.js","server/chunks/ssr/node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_59f71f6a._.js","server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_080f843b._.js","server/chunks/ssr/node_modules_@dnd-kit_core_dist_core_esm_25658ffe.js","server/chunks/ssr/node_modules_html5-qrcode_esm_07f61283._.js","server/chunks/ssr/node_modules_html5-qrcode_third_party_zxing-js_umd_e9f276bd.js","server/chunks/ssr/node_modules_@radix-ui_9ca9bb00._.js","server/chunks/ssr/node_modules_@supabase_1d116629._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_9ed906ca._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next-themes/dist/index.mjs (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/ui/sonner.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/app/components/GoogleAnalytics.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/GoogleAnalytics.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/app/components/MetaPixel.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/components/MetaPixel.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/app/post/[postId]/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/post/[postId]/error.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/components/feed/shared/PostCardSkeleton.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/feed/shared/PostCardSkeleton.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/components/post/SinglePostView.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/SinglePostView.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/components/post/BackNavigation.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/BackNavigation.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}},"[project]/components/post/ConditionalPostLayout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/post/ConditionalPostLayout.tsx (client reference/proxy)","name":"*","chunks":["server/app/post/[postId]/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/app/favicon.ico":[],"[project]/app/layout":[{"path":"static/chunks/[root-of-the-server]__acbb9135._.css","inlined":false}],"[project]/app/post/[postId]/error":[{"path":"static/chunks/[root-of-the-server]__acbb9135._.css","inlined":false}],"[project]/app/post/[postId]/loading":[{"path":"static/chunks/[root-of-the-server]__acbb9135._.css","inlined":false}],"[project]/app/post/[postId]/not-found":[{"path":"static/chunks/[root-of-the-server]__acbb9135._.css","inlined":false}],"[project]/app/post/[postId]/page":[{"path":"static/chunks/[root-of-the-server]__acbb9135._.css","inlined":false},{"path":"static/chunks/components_qr_qr-scanner_32eee468.css","inlined":false}]},"entryJSFiles":{"[project]/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/app_favicon_ico_mjs_659ce808._.js"],"[project]/app/layout":["static/chunks/_faab6129._.js","static/chunks/app_layout_tsx_c0237562._.js"],"[project]/app/post/[postId]/error":["static/chunks/_faab6129._.js","static/chunks/app_layout_tsx_c0237562._.js","static/chunks/_f2cc4642._.js","static/chunks/app_post_[postId]_error_tsx_0f887fde._.js"],"[project]/app/post/[postId]/loading":["static/chunks/_faab6129._.js","static/chunks/app_layout_tsx_c0237562._.js","static/chunks/node_modules_09cb0a30._.js","static/chunks/_79b94857._.js","static/chunks/app_post_[postId]_loading_tsx_0f887fde._.js"],"[project]/app/post/[postId]/not-found":["static/chunks/_faab6129._.js","static/chunks/app_layout_tsx_c0237562._.js","static/chunks/node_modules_next_dist_b758c999._.js","static/chunks/app_post_[postId]_not-found_tsx_0f887fde._.js"],"[project]/app/post/[postId]/page":["static/chunks/_faab6129._.js","static/chunks/app_layout_tsx_c0237562._.js","static/chunks/_16fb523b._.js","static/chunks/_16c4dad9._.js","static/chunks/node_modules_next_b6a95703._.js","static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js","static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js","static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","static/chunks/node_modules_@supabase_auth-js_dist_module_6004bf20._.js","static/chunks/node_modules_@dnd-kit_core_dist_core_esm_deb93fb9.js","static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js","static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js","static/chunks/node_modules_@radix-ui_7a78c17e._.js","static/chunks/node_modules_@supabase_4230e2a7._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_ebcf3fc1._.js","static/chunks/app_post_[postId]_page_tsx_0f887fde._.js"]}}
