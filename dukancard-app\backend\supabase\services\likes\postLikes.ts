import { supabase } from '@/lib/supabase';
import { TABLES, COLUMNS, RPC_FUNCTIONS } from '@/src/config/supabase/constants';
import type {
  LikePostRequest,
  UnlikePostRequest,
  LikePostResponse,
  UnlikePostResponse,
  PostLikeStatus,
} from '@/lib/types/like-comment';

/**
 * Like a post (business or customer)
 */
export async function likePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<LikePostResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.LIKE_POST, {
      p_post_id: postId,
      p_post_source: postSource,
    } as LikePostRequest);

    if (error) {
      console.error('Error liking post:', error);
      return {
        success: false,
        message: 'Failed to like post',
        error: error.message,
      };
    }

    return data as LikePostResponse;
  } catch (error) {
    console.error('Error in likePost:', error);
    return {
      success: false,
      message: 'Failed to like post',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Unlike a post (business or customer)
 */
export async function unlikePost(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<UnlikePostResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.UNLIKE_POST, {
      p_post_id: postId,
      p_post_source: postSource,
    } as UnlikePostRequest);

    if (error) {
      console.error('Error unliking post:', error);
      return {
        success: false,
        message: 'Failed to unlike post',
        error: error.message,
      };
    }

    return data as UnlikePostResponse;
  } catch (error) {
    console.error('Error in unlikePost:', error);
    return {
      success: false,
      message: 'Failed to unlike post',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get like status for a post
 */
export async function getPostLikeStatus(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<PostLikeStatus> {
  try {
    // Get total like count
    const { count: likeCount } = await supabase
      .from(TABLES.POST_LIKES)
      .select('*', { count: 'exact', head: true })
      .eq(COLUMNS.POST_ID, postId)
      .eq(COLUMNS.POST_SOURCE, postSource);

    // Check if current user has liked the post
    const { data: { user } } = await supabase.auth.getUser();
    let isLiked = false;

    if (user) {
      const { data: userLike } = await supabase
        .from(TABLES.POST_LIKES)
        .select(COLUMNS.ID)
        .eq(COLUMNS.USER_ID, user.id)
        .eq(COLUMNS.POST_ID, postId)
        .eq(COLUMNS.POST_SOURCE, postSource)
        .maybeSingle();

      isLiked = !!userLike;
    }

    return {
      is_liked: isLiked,
      like_count: likeCount || 0,
    };
  } catch (error) {
    console.error('Error getting post like status:', error);
    return {
      is_liked: false,
      like_count: 0,
    };
  }
}

/**
 * Get users who liked a post with their profile information
 */
export async function getPostLikers(
  postId: string,
  postSource: 'business' | 'customer',
  limit: number = 20,
  offset: number = 0
) {
  try {
    // First get the likes
    const { data: likes, error } = await supabase
      .from(TABLES.POST_LIKES)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.USER_ID}
      `)
      .eq(COLUMNS.POST_ID, postId)
      .eq(COLUMNS.POST_SOURCE, postSource)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching post likers:', error);
      return {
        success: false,
        data: [],
        error: error.message,
      };
    }

    if (!likes || likes.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Get user IDs for fetching profile information
    const userIds = likes.map((like: any) => like.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url, business_slug')
      .in('id', userIds);

    // Fetch customer profiles
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);

    // Enhance likes with user profile information
    const likersWithProfile = likes.map((like: any) => {
      const businessProfile = businessProfileMap.get(like.user_id);
      const customerProfile = customerProfileMap.get(like.user_id);

      let user_name = 'Unknown User';
      let user_avatar = undefined;
      let user_type: 'business' | 'customer' = 'customer';
      let user_slug = undefined;

      if (businessProfile) {
        user_name = businessProfile.business_name || 'Business';
        user_avatar = businessProfile.logo_url;
        user_type = 'business';
        user_slug = businessProfile.business_slug;
      } else if (customerProfile) {
        user_name = customerProfile.name || 'Customer';
        user_avatar = customerProfile.avatar_url;
        user_type = 'customer';
      }

      return {
        id: like.id,
        user_id: like.user_id,
        user_name,
        user_avatar,
        user_type,
        user_slug,
        created_at: like.created_at,
      };
    });

    return {
      success: true,
      data: likersWithProfile,
    };
  } catch (error) {
    console.error('Error in getPostLikers:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Toggle like status for a post (like if not liked, unlike if liked)
 */
export async function togglePostLike(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<LikePostResponse | UnlikePostResponse> {
  try {
    const currentStatus = await getPostLikeStatus(postId, postSource);
    
    if (currentStatus.is_liked) {
      return await unlikePost(postId, postSource);
    } else {
      return await likePost(postId, postSource);
    }
  } catch (error) {
    console.error('Error toggling post like:', error);
    return {
      success: false,
      message: 'Failed to toggle like',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
