import { create } from 'zustand';
import type { PostLikeStatus, CommentLikeStatus, PostCommentWithUser } from '@/lib/types/like-comment';
import { 
  likePost, 
  unlikePost, 
  getPostLikeStatus,
  togglePostLike 
} from '@/backend/supabase/services/likes/postLikes';
import {
  createComment as createCommentService,
  editComment as editCommentService,
  deleteComment as deleteCommentService,
  pinComment as pinCommentService,
  unpinComment as unpinCommentService,
  getPostComments as getPostCommentsService
} from '@/backend/supabase/services/comments/postComments';
import {
  likeComment,
  unlikeComment,
  getCommentLikeStatus,
  toggleCommentLike
} from '@/backend/supabase/services/likes/commentLikes';

// State interfaces
interface PostLikeState {
  [postKey: string]: PostLikeStatus; // postKey format: "postId:postSource"
}

interface CommentLikeState {
  [commentId: string]: CommentLikeStatus;
}

interface PostCommentsState {
  [postKey: string]: {
    comments: PostCommentWithUser[];
    loading: boolean;
    hasMore: boolean;
    lastFetched: number;
  };
}

interface LikeCommentStore {
  // State
  postLikes: PostLikeState;
  commentLikes: CommentLikeState;
  postComments: PostCommentsState;
  
  // Loading states
  likingPosts: Set<string>; // postKeys currently being liked/unliked
  likingComments: Set<string>; // commentIds currently being liked/unliked
  loadingComments: Set<string>; // postKeys currently loading comments
  
  // Actions for post likes
  likePost: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  unlikePost: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  togglePostLike: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  getPostLikeStatus: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  
  // Actions for comment likes
  likeComment: (commentId: string) => Promise<void>;
  unlikeComment: (commentId: string) => Promise<void>;
  toggleCommentLike: (commentId: string) => Promise<void>;
  getCommentLikeStatus: (commentId: string) => Promise<void>;

  // Actions for comments
  createComment: (postId: string, postSource: 'business' | 'customer', content: string, parentCommentId?: string) => Promise<boolean>;
  editComment: (commentId: string, content: string) => Promise<boolean>;
  deleteComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  pinComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  unpinComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  loadComments: (postId: string, postSource: 'business' | 'customer', refresh?: boolean) => Promise<void>;

  // Utility actions
  clearCache: () => void;
  clearPostCache: (postId: string, postSource: 'business' | 'customer') => void;
}

const createPostKey = (postId: string, postSource: 'business' | 'customer') => `${postId}:${postSource}`;

export const useLikeCommentStore = create<LikeCommentStore>()((set, get) => ({
        // Initial state
        postLikes: {},
        commentLikes: {},
        postComments: {},
        likingPosts: new Set(),
        likingComments: new Set(),
        loadingComments: new Set(),

        // Post like actions
        likePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              } else {
                state.postLikes[postKey] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.postLikes[postKey] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = false;
                  state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });
          } finally {
            set((state) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        unlikePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });

            const result = await unlikePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.postLikes[postKey] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = true;
                  state.postLikes[postKey].like_count += 1;
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              }
            });
          } finally {
            set((state) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        togglePostLike: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentStatus = get().postLikes[postKey];
          
          if (currentStatus?.is_liked) {
            await get().unlikePost(postId, postSource);
          } else {
            await get().likePost(postId, postSource);
          }
        },

        getPostLikeStatus: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          try {
            const status = await getPostLikeStatus(postId, postSource);
            set((state) => {
              state.postLikes[postKey] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (error) {
            console.error('Error fetching post like status:', error);
          }
        },

        // Comment like actions
        likeComment: async (commentId: string) => {
          set((state) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              } else {
                state.commentLikes[commentId] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.commentLikes[commentId] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = false;
                  state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });
          } finally {
            set((state) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        unlikeComment: async (commentId: string) => {
          set((state) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });

            const result = await unlikeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.commentLikes[commentId] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = true;
                  state.commentLikes[commentId].like_count += 1;
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              }
            });
          } finally {
            set((state) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        toggleCommentLike: async (commentId: string) => {
          const currentStatus = get().commentLikes[commentId];
          
          if (currentStatus?.is_liked) {
            await get().unlikeComment(commentId);
          } else {
            await get().likeComment(commentId);
          }
        },

        getCommentLikeStatus: async (commentId: string) => {
          try {
            const status = await getCommentLikeStatus(commentId);
            set((state) => {
              state.commentLikes[commentId] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (error) {
            console.error('Error fetching comment like status:', error);
          }
        },

        // Utility actions
        clearCache: () => {
          set((state) => {
            state.postLikes = {};
            state.commentLikes = {};
            state.postComments = {};
          });
        },

        clearPostCache: (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentState = get();
          const newPostLikes = { ...currentState.postLikes };
          const newPostComments = { ...currentState.postComments };
          delete newPostLikes[postKey];
          delete newPostComments[postKey];
          set({
            ...currentState,
            postLikes: newPostLikes,
            postComments: newPostComments,
          });
        },

        // Comment CRUD functions
        createComment: async (postId: string, postSource: 'business' | 'customer', content: string, parentCommentId?: string) => {
          try {
            const result = await createCommentService(postId, postSource, content, parentCommentId);

            if (result.success) {
              // Refresh comments for this post to show the new comment
              const postKey = createPostKey(postId, postSource);
              const currentState = get();
              const newLoadingComments = new Set(currentState.loadingComments);
              newLoadingComments.add(postKey);

              set({
                ...currentState,
                loadingComments: newLoadingComments
              });

              try {
                const commentsResult = await getPostCommentsService({
                  post_id: postId,
                  post_source: postSource,
                  sort_order: 'pinned_first',
                });

                if (commentsResult.success) {
                  const updatedState = get();
                  const finalLoadingComments = new Set(updatedState.loadingComments);
                  finalLoadingComments.delete(postKey);

                  set({
                    ...updatedState,
                    postComments: {
                      ...updatedState.postComments,
                      [postKey]: {
                        comments: commentsResult.data,
                        loading: false,
                        hasMore: commentsResult.data.length === 50,
                        lastFetched: Date.now(),
                      }
                    },
                    loadingComments: finalLoadingComments
                  });
                }
              } catch (error) {
                console.error('Error refreshing comments after creation:', error);
                const errorState = get();
                const errorLoadingComments = new Set(errorState.loadingComments);
                errorLoadingComments.delete(postKey);

                set({
                  ...errorState,
                  loadingComments: errorLoadingComments
                });
              }
            }

            return result.success;
          } catch (error) {
            console.error('Error in createComment:', error);
            return false;
          }
        },

        editComment: async (commentId: string, content: string) => {
          try {
            const result = await editCommentService(commentId, content);

            if (result.success) {
              // Update the comment in the store
              const currentState = get();
              const newPostComments = { ...currentState.postComments };

              // Find and update the comment in all post comment lists
              Object.keys(newPostComments).forEach(postKey => {
                const postCommentsData = newPostComments[postKey];
                if (postCommentsData?.comments) {
                  const updatedComments = postCommentsData.comments.map(comment => {
                    if (comment.id === commentId) {
                      return {
                        ...comment,
                        content,
                        is_edited: true,
                        edited_at: new Date().toISOString(),
                      };
                    }
                    return comment;
                  });

                  newPostComments[postKey] = {
                    ...postCommentsData,
                    comments: updatedComments,
                  };
                }
              });

              set({ postComments: newPostComments });
            }

            return result.success;
          } catch (error) {
            console.error('Error in editComment:', error);
            return false;
          }
        },

        deleteComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const result = await deleteCommentService(commentId);

            if (result.success) {
              // Remove the comment from the store
              const postKey = createPostKey(postId, postSource);
              const currentState = get();
              const postCommentsData = currentState.postComments[postKey];

              if (postCommentsData?.comments) {
                const updatedComments = postCommentsData.comments.filter(comment => comment.id !== commentId);

                const currentState = get();
                set({
                  ...currentState,
                  postComments: {
                    ...currentState.postComments,
                    [postKey]: {
                      ...postCommentsData,
                      comments: updatedComments,
                    }
                  }
                });
              }
            }

            return result.success;
          } catch (error) {
            console.error('Error in deleteComment:', error);
            return false;
          }
        },

        pinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const result = await pinCommentService(commentId);

            if (result.success) {
              // Update the comment's pinned status in the store
              const postKey = createPostKey(postId, postSource);
              const currentState = get();
              const postCommentsData = currentState.postComments[postKey];

              if (postCommentsData?.comments) {
                const updatedComments = postCommentsData.comments.map(comment => {
                  if (comment.id === commentId) {
                    return { ...comment, is_pinned: true };
                  }
                  return comment;
                });

                const currentState = get();
                set({
                  ...currentState,
                  postComments: {
                    ...currentState.postComments,
                    [postKey]: {
                      ...postCommentsData,
                      comments: updatedComments,
                    }
                  }
                });
              }
            }

            return result.success;
          } catch (error) {
            console.error('Error in pinComment:', error);
            return false;
          }
        },

        unpinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const result = await unpinCommentService(commentId);

            if (result.success) {
              // Update the comment's pinned status in the store
              const postKey = createPostKey(postId, postSource);
              const currentState = get();
              const postCommentsData = currentState.postComments[postKey];

              if (postCommentsData?.comments) {
                const updatedComments = postCommentsData.comments.map(comment => {
                  if (comment.id === commentId) {
                    return { ...comment, is_pinned: false };
                  }
                  return comment;
                });

                set({
                  ...currentState,
                  postComments: {
                    ...currentState.postComments,
                    [postKey]: {
                      ...postCommentsData,
                      comments: updatedComments,
                    }
                  }
                });
              }
            }

            return result.success;
          } catch (error) {
            console.error('Error in unpinComment:', error);
            return false;
          }
        },

        loadComments: async (postId: string, postSource: 'business' | 'customer', refresh = false) => {
          const postKey = createPostKey(postId, postSource);
          
          // Check if already loading
          if (get().loadingComments.has(postKey)) {
            return;
          }

          // Check cache (5 minutes)
          const cached = get().postComments[postKey];
          if (!refresh && cached && (Date.now() - cached.lastFetched) < 5 * 60 * 1000) {
            return;
          }

          const currentState = get();
          const newLoadingComments = new Set(currentState.loadingComments);
          newLoadingComments.add(postKey);

          set({
            ...currentState,
            loadingComments: newLoadingComments
          });

          try {
            const result = await getPostCommentsService({
              post_id: postId,
              post_source: postSource,
              sort_order: 'pinned_first',
              limit: 50,
              offset: 0
            });

            if (result.success) {
              const updatedState = get();
              set({
                ...updatedState,
                postComments: {
                  ...updatedState.postComments,
                  [postKey]: {
                    comments: result.data,
                    loading: false,
                    hasMore: result.data.length === 50,
                    lastFetched: Date.now()
                  }
                }
              });
            }
          } catch (error) {
            console.error('Error loading comments:', error);
          } finally {
            const finalState = get();
            const finalLoadingComments = new Set(finalState.loadingComments);
            finalLoadingComments.delete(postKey);

            set({
              ...finalState,
              loadingComments: finalLoadingComments
            });
          }
        },
}));
