import { supabase } from '@/lib/supabase';
import { TABLES, COLUMNS, RPC_FUNCTIONS } from '@/src/config/supabase/constants';
import type {
  CreateCommentRequest,
  EditCommentRequest,
  DeleteCommentRequest,
  PinCommentRequest,
  UnpinCommentRequest,
  CreateCommentResponse,
  EditCommentResponse,
  DeleteCommentResponse,
  PinCommentResponse,
  UnpinCommentResponse,
  PostCommentWithUser,
  CommentFilters,
} from '@/lib/types/like-comment';

/**
 * Create a new comment on a post
 */
export async function createComment(
  postId: string,
  postSource: 'business' | 'customer',
  content: string,
  parentCommentId?: string
): Promise<CreateCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.CREATE_COMMENT as any, {
      p_post_id: postId,
      p_post_source: postSource,
      p_content: content,
      p_parent_comment_id: parentCommentId || null,
    } as CreateCommentRequest);

    if (error) {
      console.error('Error creating comment:', error);
      return {
        success: false,
        message: 'Failed to create comment',
        error: error.message,
      };
    }

    return data as unknown as CreateCommentResponse;
  } catch (error) {
    console.error('Error in createComment:', error);
    return {
      success: false,
      message: 'Failed to create comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Edit an existing comment
 */
export async function editComment(
  commentId: string,
  content: string
): Promise<EditCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.EDIT_COMMENT, {
      p_comment_id: commentId,
      p_content: content,
    } as EditCommentRequest);

    if (error) {
      console.error('Error editing comment:', error);
      return {
        success: false,
        message: 'Failed to edit comment',
        error: error.message,
      };
    }

    return data as unknown as EditCommentResponse;
  } catch (error) {
    console.error('Error in editComment:', error);
    return {
      success: false,
      message: 'Failed to edit comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Delete a comment
 */
export async function deleteComment(commentId: string): Promise<DeleteCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.DELETE_COMMENT, {
      p_comment_id: commentId,
    } as DeleteCommentRequest);

    if (error) {
      console.error('Error deleting comment:', error);
      return {
        success: false,
        message: 'Failed to delete comment',
        error: error.message,
      };
    }

    return data as unknown as DeleteCommentResponse;
  } catch (error) {
    console.error('Error in deleteComment:', error);
    return {
      success: false,
      message: 'Failed to delete comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Pin a comment (post owner only)
 */
export async function pinComment(commentId: string): Promise<PinCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.PIN_COMMENT, {
      p_comment_id: commentId,
    } as PinCommentRequest);

    if (error) {
      console.error('Error pinning comment:', error);
      return {
        success: false,
        message: 'Failed to pin comment',
        error: error.message,
      };
    }

    return data as unknown as PinCommentResponse;
  } catch (error) {
    console.error('Error in pinComment:', error);
    return {
      success: false,
      message: 'Failed to pin comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Unpin a comment (post owner only)
 */
export async function unpinComment(commentId: string): Promise<UnpinCommentResponse> {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.UNPIN_COMMENT, {
      p_comment_id: commentId,
    } as UnpinCommentRequest);

    if (error) {
      console.error('Error unpinning comment:', error);
      return {
        success: false,
        message: 'Failed to unpin comment',
        error: error.message,
      };
    }

    return data as unknown as UnpinCommentResponse;
  } catch (error) {
    console.error('Error in unpinComment:', error);
    return {
      success: false,
      message: 'Failed to unpin comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get replies for a specific comment
 */
async function getCommentReplies(
  commentId: string,
  currentUserId?: string
): Promise<PostCommentWithUser[]> {
  try {
    // Fetch replies for this comment
    const { data: replies, error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.USER_ID},
        ${COLUMNS.POST_ID},
        ${COLUMNS.POST_SOURCE},
        ${COLUMNS.PARENT_COMMENT_ID},
        ${COLUMNS.CONTENT},
        ${COLUMNS.IS_PINNED},
        ${COLUMNS.IS_EDITED},
        ${COLUMNS.EDITED_AT},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT}
      `)
      .eq(COLUMNS.PARENT_COMMENT_ID, commentId)
      .order(COLUMNS.CREATED_AT, { ascending: true });

    if (error || !replies || replies.length === 0) {
      return [];
    }

    // Get user IDs for fetching user information
    const userIds = replies.map(reply => reply.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url')
      .in('id', userIds);

    // Fetch customer profiles
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Fetch reply like counts
    const { data: likeCounts } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select('comment_id')
      .in('comment_id', replies.map(r => r.id));

    // Fetch current user's likes on these replies
    let userLikes: any[] = [];
    if (currentUserId) {
      const { data } = await supabase
        .from(TABLES.COMMENT_LIKES)
        .select('comment_id')
        .eq('user_id', currentUserId)
        .in('comment_id', replies.map(r => r.id));
      userLikes = data || [];
    }

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);
    const likeCountMap = new Map();
    const userLikeMap = new Set(userLikes.map(ul => ul.comment_id));

    // Count likes per reply
    likeCounts?.forEach(like => {
      const count = likeCountMap.get(like.comment_id) || 0;
      likeCountMap.set(like.comment_id, count + 1);
    });

    // Enhance replies with user information
    const repliesWithUser: PostCommentWithUser[] = replies.map(reply => {
      const businessProfile = businessProfileMap.get(reply.user_id);
      const customerProfile = customerProfileMap.get(reply.user_id);

      let user_name = 'Unknown User';
      let user_avatar = undefined;
      let user_type: 'business' | 'customer' = 'customer';

      if (businessProfile) {
        user_name = businessProfile.business_name;
        user_avatar = businessProfile.logo_url;
        user_type = 'business';
      } else if (customerProfile) {
        user_name = customerProfile.name || 'Customer';
        user_avatar = customerProfile.avatar_url;
        user_type = 'customer';
      }

      return {
        ...reply,
        user_name,
        user_avatar,
        user_type,
        like_count: likeCountMap.get(reply.id) || 0,
        is_liked_by_current_user: userLikeMap.has(reply.id),
        replies: [], // Replies don't have nested replies (only 2 levels deep)
      };
    });

    return repliesWithUser;
  } catch (error) {
    console.error('Error fetching comment replies:', error);
    return [];
  }
}

/**
 * Get comments for a post with user information
 */
export async function getPostComments(
  filters: CommentFilters
): Promise<{ success: boolean; data: PostCommentWithUser[]; error?: string }> {
  try {
    const { post_id, post_source, sort_order = 'pinned_first', limit = 20, offset = 0 } = filters;

    // Get current user ID for like status checking
    const { data: { user } } = await supabase.auth.getUser();
    const currentUserId = user?.id;

    // Build the query with user information
    let query = supabase
      .from(TABLES.POST_COMMENTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.USER_ID},
        ${COLUMNS.POST_ID},
        ${COLUMNS.POST_SOURCE},
        ${COLUMNS.PARENT_COMMENT_ID},
        ${COLUMNS.CONTENT},
        ${COLUMNS.IS_PINNED},
        ${COLUMNS.IS_EDITED},
        ${COLUMNS.EDITED_AT},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT}
      `)
      .eq(COLUMNS.POST_ID, post_id)
      .eq(COLUMNS.POST_SOURCE, post_source)
      .is(COLUMNS.PARENT_COMMENT_ID, null); // Only top-level comments

    // Apply sorting
    if (sort_order === 'pinned_first') {
      query = query.order(COLUMNS.IS_PINNED, { ascending: false })
                   .order(COLUMNS.CREATED_AT, { ascending: true });
    } else if (sort_order === 'newest') {
      query = query.order(COLUMNS.CREATED_AT, { ascending: false });
    } else {
      query = query.order(COLUMNS.CREATED_AT, { ascending: true });
    }

    const { data: comments, error } = await query.range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching comments:', error);
      return {
        success: false,
        data: [],
        error: error.message,
      };
    }

    if (!comments || comments.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Get user IDs for fetching user information
    const userIds = comments.map(comment => comment.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url')
      .in('id', userIds);

    // Fetch customer profiles (using public view for security)
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Fetch comment like counts
    const { data: likeCounts } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select('comment_id')
      .in('comment_id', comments.map(c => c.id));

    // Fetch current user's likes on these comments
    let userLikes: any[] = [];
    if (currentUserId) {
      const { data } = await supabase
        .from(TABLES.COMMENT_LIKES)
        .select('comment_id')
        .eq('user_id', currentUserId)
        .in('comment_id', comments.map(c => c.id));
      userLikes = data || [];
    }

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);
    const likeCountMap = new Map();
    const userLikeMap = new Set(userLikes.map(ul => ul.comment_id));

    // Count likes per comment
    likeCounts?.forEach(like => {
      const count = likeCountMap.get(like.comment_id) || 0;
      likeCountMap.set(like.comment_id, count + 1);
    });

    // Enhance comments with user information and like data
    const commentsWithUser: PostCommentWithUser[] = await Promise.all(
      comments.map(async (comment) => {
        const businessProfile = businessProfileMap.get(comment.user_id);
        const customerProfile = customerProfileMap.get(comment.user_id);

        let user_name = 'Unknown User';
        let user_avatar = undefined;
        let user_type: 'business' | 'customer' = 'customer';

        if (businessProfile) {
          user_name = businessProfile.business_name;
          user_avatar = businessProfile.logo_url;
          user_type = 'business';
        } else if (customerProfile) {
          user_name = customerProfile.name || 'Customer';
          user_avatar = customerProfile.avatar_url;
          user_type = 'customer';
        }

        // Fetch replies for this comment
        const replies = await getCommentReplies(comment.id, currentUserId);

        return {
          ...comment,
          user_name,
          user_avatar,
          user_type,
          like_count: likeCountMap.get(comment.id) || 0,
          is_liked_by_current_user: userLikeMap.has(comment.id),
          replies,
        };
      })
    );

    return {
      success: true,
      data: commentsWithUser,
    };
  } catch (error) {
    console.error('Error in getPostComments:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
